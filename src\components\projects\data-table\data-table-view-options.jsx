"use client";

import React, { useCallback, useState, useEffect, useRef } from "react";
import { DropdownMenuTrigger } from "@radix-ui/react-dropdown-menu";
import { Settings2 } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";

function DataTableViewOptionsComponent({ table }) {
  // Use state to track column visibility changes and force rerenders (similar to pagination)
  const [columnVisibilityState, setColumnVisibilityState] = useState(
    () => table?.getState().columnVisibility || {}
  );

  // Update local state when table instance changes (not on every render)
  useEffect(() => {
    if (!table) return;
    const currentVisibility = table.getState().columnVisibility;
    setColumnVisibilityState(currentVisibility);
  }, [table]);

  // Use ref to track previous state for comparison
  const previousStateRef = useRef();

  // Listen for external column visibility changes (e.g., from column header hide button)
  useEffect(() => {
    if (!table) return;

    const currentVisibility = table.getState().columnVisibility;
    const currentStateString = JSON.stringify(currentVisibility);

    // Check if state changed from external source
    if (
      previousStateRef.current &&
      previousStateRef.current !== currentStateString
    ) {
      setColumnVisibilityState(currentVisibility);
    }

    // Update ref with current state
    previousStateRef.current = currentStateString;
  }); // No dependency array - runs on every render to detect external changes

  // Memoize the columns to prevent unnecessary recalculations
  const hidableColumns = React.useMemo(() => {
    if (!table) return [];

    return table
      .getAllColumns()
      .filter(
        (column) =>
          typeof column.accessorFn !== "undefined" && column.getCanHide()
      );
  }, [table]);

  // Optimized toggle handler with immediate UI feedback
  const handleColumnToggle = useCallback((column, value) => {
    // Update local state first for immediate UI feedback
    setColumnVisibilityState((prev) => ({
      ...prev,
      [column.id]: !!value,
    }));

    // Use requestAnimationFrame for smoother UI updates
    requestAnimationFrame(() => {
      column.toggleVisibility(!!value);
    });
  }, []);

  if (!table) return null;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="icon">
          <Settings2 className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[150px]">
        <DropdownMenuLabel>Toggle columns</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {hidableColumns.map((column) => {
          // Use local state for checked status (similar to pagination)
          const isVisible = columnVisibilityState[column.id] !== false;

          return (
            <DropdownMenuCheckboxItem
              key={column.id}
              className="capitalize"
              checked={isVisible}
              onCheckedChange={(value) => handleColumnToggle(column, value)}
            >
              {column.id}
            </DropdownMenuCheckboxItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Memoize component to prevent unnecessary rerenders
export const DataTableViewOptions = React.memo(DataTableViewOptionsComponent);
